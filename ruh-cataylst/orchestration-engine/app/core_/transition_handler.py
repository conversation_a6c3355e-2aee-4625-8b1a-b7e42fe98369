import asyncio
import json
import time
import traceback
from typing import Any, List, Dict
from app.utils.helper_functions import format_execution_result
from app.utils.enhanced_logger import get_logger
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.node_executor import NodeExecutor
from app.services.loop_executor import LoopExecutor

logger = get_logger("TransitionHandler")


class TransitionHandler:

    def __init__(
        self,
        state_manager,
        transitions_by_id,
        nodes,
        dependency_map,
        workflow_utils,
        tool_executor,
        node_executor=None,
        agent_executor=None,
        result_callback=None,
        approval=False,
        user_id=None,
    ):
        self.state_manager = state_manager
        self.transitions_by_id = transitions_by_id
        self.nodes = nodes
        self.result_callback = result_callback
        self.dependency_map = dependency_map
        self.workflow_utils = workflow_utils
        self.tool_executor = tool_executor  # MCP server executor
        self.node_executor = node_executor  # Node executor for Components
        self.agent_executor = agent_executor  # Agent executor for Agent tasks
        self.user_id = user_id  # User ID for tool execution

        # Flags
        self.current_transition_id = None
        self.approval = approval
        self.workflow_paused = False
        self._pause_event = asyncio.Event()

        # Orchestration engine reference for loop coordination
        self.orchestration_engine = None

        self.logger = logger

        # Initialize conditional routing handler
        from app.core_.conditional_routing_handler import ConditionalRoutingHandler

        self.conditional_routing_handler = ConditionalRoutingHandler(logger=self.logger)

        # Initialize completion callback system for loop body chain integration
        self._completion_callbacks = {}  # transition_id -> callback function
        self._active_loop_executors = {}  # transition_id -> loop_executor instance

        self.logger.info("TransitionHandler initialized")

    async def register_completion_callback(self, transition_id: str, callback_func) -> None:
        """
        Register a callback function to be called when a transition completes.

        This is used by loop body chain executors to be notified when
        transitions in their chains complete.

        Args:
            transition_id: ID of the transition to monitor
            callback_func: Async function to call when transition completes
        """
        self._completion_callbacks[transition_id] = callback_func
        self.logger.debug(
            f"📝 Registered completion callback for transition: {transition_id}"
        )

    async def unregister_completion_callback(self, transition_id: str) -> None:
        """
        Unregister a completion callback for a transition.

        Args:
            transition_id: ID of the transition to stop monitoring
        """
        if transition_id in self._completion_callbacks:
            del self._completion_callbacks[transition_id]
            self.logger.debug(
                f"🗑️ Unregistered completion callback for transition: {transition_id}"
            )

    def register_loop_executor(self, transition_id: str, loop_executor) -> None:
        """
        Register a loop executor for a specific transition.

        This allows the transition handler to notify the loop executor
        about transition completions during loop body chain execution.

        Args:
            transition_id: ID of the loop transition
            loop_executor: The LoopExecutor instance handling this transition
        """
        self._active_loop_executors[transition_id] = loop_executor
        self.logger.debug(
            f"📝 Registered loop executor for transition: {transition_id}"
        )

    def unregister_loop_executor(self, transition_id: str) -> None:
        """
        Unregister a loop executor for a transition.

        Args:
            transition_id: ID of the loop transition
        """
        if transition_id in self._active_loop_executors:
            del self._active_loop_executors[transition_id]
            self.logger.debug(
                f"🗑️ Unregistered loop executor for transition: {transition_id}"
            )

    async def _notify_transition_completion(self, transition_id: str, result: Any) -> None:
        """
        Notify registered callbacks and loop executors about transition completion.

        This method is called whenever a transition completes to inform
        any registered loop body chain executors or other interested parties.

        Args:
            transition_id: ID of the completed transition
            result: Result from the completed transition
        """
        # Notify registered completion callbacks
        if transition_id in self._completion_callbacks:
            try:
                callback_func = self._completion_callbacks[transition_id]
                await callback_func(transition_id, result)
                self.logger.debug(
                    f"✅ Notified completion callback for transition: {transition_id}"
                )
            except Exception as e:
                self.logger.error(
                    f"❌ Error in completion callback for transition {transition_id}: {str(e)}"
                )

        # Notify any active loop executors that might be interested
        for loop_transition_id, loop_executor in self._active_loop_executors.items():
            try:
                # The loop executor will determine if this transition is relevant to its chains
                # Note: notify_transition_completion is NOT async, so don't await it
                loop_executor.notify_transition_completion(transition_id, result)
                self.logger.debug(
                    f"✅ Notified loop executor {loop_transition_id} about transition {transition_id}"
                )
            except Exception as e:
                self.logger.error(
                    f"❌ Error notifying loop executor {loop_transition_id} about transition {transition_id}: {str(e)}"
                )

    def _find_initial_transition(self):
        """
        Returns a list of transitions with transition_type='initial' if present,
        otherwise a list of all transitions sorted by sequence number.

        This allows for multiple initial transitions to be executed in parallel
        when multiple transitions have transition_type='initial'.
        """
        initial_transitions = [
            s
            for s in self.transitions_by_id.values()
            if s["transition_type"] == "initial"
        ]
        if initial_transitions:
            return initial_transitions

        # If no initial transitions found, use the transition with the smallest sequence number
        if self.transitions_by_id:
            return [
                sorted(self.transitions_by_id.values(), key=lambda s: s["sequence"])[0]
            ]

        raise Exception("No initial transitions found")

    async def _execute_transition_with_tracking(self, transition):
        """
        Execute a transition with proper tracking and monitoring to ensure it completes.
        This wrapper helps identify and debug issues with parallel execution.
        """

        transition_id = transition["id"]
        try:
            self.logger.info(
                f"Starting parallel execution of transition: {transition_id}"
            )
            result_info = {
                "result": f"Starting execution of transition: {transition_id}",
                "message": "Starting execution...",
                "transition_id": transition_id,
                "status": "started",
            }
            await self.result_callback(result_info)
            start_time = asyncio.get_event_loop().time()

            # Execute the transition
            result = await self._execute_standard_or_reflection_transition(transition)

            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            self.logger.info(
                f"Completed transition {transition_id} in {execution_time:.2f} seconds"
            )
            result_info = {
                "result": f"Completed transition in {execution_time:.2f} seconds",
                "message": f"Transition completed in {execution_time:.2f} seconds",
                "transition_id": transition_id,
                "status": "time_logged",
            }
            await self.result_callback(result_info)
            # Verify the transition was properly marked as completed
            if transition_id not in self.state_manager.completed_transitions:
                self.logger.warning(
                    f"Transition {transition_id} executed but not marked as completed!"
                )

            return result
        except Exception as e:
            self.logger.error(f"Exception in transition {transition_id}: {str(e)}")
            raise Exception(f"Exception in transition {transition_id}: {str(e)}")

    async def _execute_standard_or_reflection_transition(
        self, transition: dict, server_params_override=None, action_type=None
    ) -> list[str]:
        """
        Executes a 'standard' or 'reflection' transition.
        Assumes each transition in this function executes exactly one node.
        Returns the next transition IDs and the result of the execution.
        """
        transition_id = transition["id"]
        transition_type = transition["transition_type"]
        execution_type = transition.get(
            "execution_type", "MCP"
        )  # Default to MCP for backward compatibility
        self.logger.execute(
            f"Transition '{transition_id}' (type={transition_type}, execution_type={execution_type})"
        )

        node_info = transition.get("node_info", {})
        if not node_info:
            self.logger.error(
                f"No node_info specified for transition '{transition_id}'"
            )
            raise AttributeError(
                f"No node_info specified for transition '{transition_id}'"
            )

        node_id = node_info.get("node_id")
        tools_to_use = node_info.get("tools_to_use", [])
        output_data_configs = node_info.get("output_data", [])
        input_data_configs = node_info.get("input_data", [])
        next_transitions = []

        node_details = self.nodes.get(node_id)
        if not node_details:
            self.logger.error(f"Node details for '{node_id}' not found in schema")
            raise AttributeError(f"Node details for '{node_id}' not found in schema")

        server_tools_data = node_details.get("server_tools", [])
        if not server_tools_data:
            self.logger.error(f"No server tools defined for node '{node_id}'")
            raise AttributeError(f"No server tools defined for node '{node_id}'")

        tool_results = {}
        execution_result = None
        approval_required = transition.get("approval_required", False)

        # Determine which executor to use based on execution_type
        executor = self._get_executor_for_type(execution_type)
        if not executor:
            self.logger.error(
                f"No executor available for execution type: {execution_type}"
            )
            raise ValueError(
                f"No executor available for execution type: {execution_type}"
            )

        # Log which executor is being used
        if executor == self.node_executor:
            executor_type = "NodeExecutor"
        elif executor == self.agent_executor:
            executor_type = "AgentExecutor"
        else:
            executor_type = "KafkaToolExecutor"
        self.logger.info(f"Using {executor_type} for execution_type: {execution_type}")

        for tool_config in tools_to_use:
            tool_id = tool_config.get("tool_id")
            tool_name = tool_config.get("tool_name")
            tool_params_config = tool_config.get("tool_params", {"items": []}).get(
                "items"
            )

            node_tool_info = next(
                (tool for tool in server_tools_data if tool["tool_name"] == tool_name),
                None,
            )
            if not node_tool_info:
                self.logger.error(
                    f"Tool with name '{tool_name}' not found for node '{node_id}'"
                )
                node_tool_info = next(
                    (tool for tool in server_tools_data if tool["tool_id"] == tool_id),
                    None,
                )
                if not node_tool_info:
                    self.logger.error(
                        f"Tool with id '{tool_id}' not found for node '{node_id}'"
                    )
                    continue

            result_info = {
                "transition_id": transition_id,
                "node_id": node_id,
                "tool_name": tool_name,
                "message": "",
                "result": None,
            }
            try:
                # Use universal handle-based parameter resolution
                tool_parameters = await self._resolve_tool_parameters_universally(
                    node_tool_info,
                    input_data_configs,
                    transition_id,
                    tool_params_config,
                    transition,
                )
                self.logger.debug(f"tool Parameters: {tool_parameters}")
                self.logger.info(
                    f"Invoking tool '{tool_name}' (tool_id: {tool_id}) for node '{node_id}' in transition '{transition_id}' with parameters: {tool_parameters}"
                )
                result_info["result"] = f"Connecting to server {node_id}"
                result_info["message"] = f"Connecting to server"
                result_info["status"] = "connecting"
                await self.result_callback(result_info)
                if server_params_override:

                    def find_and_update_parameter(
                        param_dict, param_name, override_value, path=""
                    ):
                        """Recursively search for parameter in nested dictionaries and update if found."""
                        found = False

                        if isinstance(param_dict, dict):

                            if param_name in param_dict:
                                param_dict[param_name] = override_value
                                self.logger.debug(
                                    f"Parameter '{path+param_name}' for transition '{transition_id}' overridden with value: {override_value}"
                                )
                                found = True

                            for key, value in param_dict.items():
                                if isinstance(value, dict):
                                    nested_found = find_and_update_parameter(
                                        value,
                                        param_name,
                                        override_value,
                                        f"{path}{key}.",
                                    )
                                    found = found or nested_found

                        return found

                    for param_name, override_value in server_params_override.items():
                        found = find_and_update_parameter(
                            tool_parameters, param_name, override_value
                        )

                        if not found:
                            self.logger.warning(
                                f"Parameter '{param_name}' from server_params_override not found in tool_parameters for transition '{transition_id}'. Ignoring override."
                            )

                # Execute the tool using the appropriate executor based on execution_type
                if execution_type == "loop":
                    # For Loop executor, pass the loop configuration
                    loop_config = transition.get("loop_config")
                    if not loop_config:
                        raise ValueError(
                            f"Loop configuration missing for loop transition: {transition_id}"
                        )

                    # Resolve variables in loop_config using existing parameter resolution
                    resolved_loop_config = await self._resolve_loop_config_parameters(
                        loop_config, transition_id
                    )

                    # Update loop config with resolved iteration_list from tool parameters
                    if tool_parameters and 'iteration_list' in tool_parameters:
                        iteration_source = resolved_loop_config.get("iteration_source", {})
                        if 'iteration_list' in iteration_source:
                            # Update the iteration_list in loop config with resolved data
                            iteration_source['iteration_list'] = tool_parameters['iteration_list']
                            self.logger.debug(
                                f"🔧 Updated loop config iteration_list with resolved data: {tool_parameters['iteration_list']}"
                            )

                    # Auto-detect loop body transitions if not explicitly configured
                    loop_body_transitions = resolved_loop_config.get("loop_body_transitions", [])

                    # Check for new schema format (loop_body_configuration)
                    loop_body_config = resolved_loop_config.get("loop_body_configuration", {})
                    entry_transitions = loop_body_config.get("entry_transitions", [])
                    exit_transitions = loop_body_config.get("exit_transitions", [])

                    # If we have explicit configuration in new format, use it
                    if entry_transitions or exit_transitions:
                        # Combine entry and exit transitions as loop body transitions for legacy compatibility
                        loop_body_transitions = list(set(entry_transitions + exit_transitions))
                        resolved_loop_config["loop_body_transitions"] = loop_body_transitions
                        self.logger.info(
                            f"🔍 Using explicit loop body configuration: entry={entry_transitions}, exit={exit_transitions}"
                        )
                    elif not loop_body_transitions:
                        loop_body_transitions = self._auto_detect_loop_body_transitions(
                            output_data_configs
                        )
                        if loop_body_transitions:
                            resolved_loop_config["loop_body_transitions"] = loop_body_transitions
                            self.logger.info(
                                f"🔍 Auto-detected and added loop body transitions to config: {loop_body_transitions}"
                            )

                    # Prepare input data from resolved tool parameters for loop executor
                    loop_input_data = tool_parameters if tool_parameters else {}

                    # Prepare output routing from output data configs
                    loop_output_routing = {
                        "output_configs": output_data_configs
                    }

                    execution_result = await executor.execute_tool(
                        tool_name=tool_name,
                        tool_parameters=tool_parameters,
                        loop_config=resolved_loop_config,
                        transition_id=transition_id,
                        input_data=loop_input_data,
                        output_routing=loop_output_routing,
                        input_data_configs=input_data_configs,
                    )
                elif executor == self.tool_executor:
                    # Validate user_id is available for MCP tool execution
                    if not self.user_id:
                        self.logger.error(
                            f"user_id is required for MCP tool execution but not provided"
                        )
                        raise ValueError("user_id is required for tool execution")

                    # For MCP executor, pass the node_id as mcp_id (no server_script_path needed)
                    execution_result = await executor.execute_tool(
                        tool_name=tool_name,
                        tool_parameters=tool_parameters,
                        mcp_id=node_id,
                        transition_id=transition_id,
                    )
                elif executor == self.agent_executor:
                    # Validate user_id is available for agent tool execution
                    if not self.user_id:
                        self.logger.error(
                            f"user_id is required for agent tool execution but not provided"
                        )
                        raise ValueError("user_id is required for tool execution")

                    # For Agent executor, pass the node_id as agent_id (no server_script_path needed)
                    execution_result = await executor.execute_tool(
                        tool_name=tool_name,
                        tool_parameters=tool_parameters,
                        transition_id=transition_id,
                    )
                else:
                    # Validate user_id is available for other tool execution
                    if not self.user_id:
                        self.logger.error(
                            f"user_id is required for tool execution but not provided"
                        )
                        raise ValueError("user_id is required for tool execution")

                    # For other executors (like NodeExecutor), use the original signature (no server_script_path needed)
                    execution_result = await executor.execute_tool(
                        tool_name=tool_name,
                        tool_parameters=tool_parameters,
                        transition_id=transition_id,
                    )

                # Log the execution result for debugging
                self.logger.info(
                    f"Execution result from {execution_type} executor: {json.dumps(execution_result, indent=2)}"
                )

                # Check for errors in the execution result
                if isinstance(execution_result, dict):
                    # Log the execution result for debugging
                    self.logger.info(
                        f"Checking execution result for errors: {json.dumps(execution_result, indent=2)}"
                    )

                    # Check if the status is error
                    if execution_result.get("status") == "error":
                        # Handle different error formats from different executors
                        error_message = None

                        # Format 1: Direct error message in 'error' field (from kafka_tool_executor.py)
                        if "error" in execution_result:
                            error_message = execution_result.get("error")
                            self.logger.info(
                                f"Found direct error in execution result: {error_message}"
                            )

                        # Format 2: Error message in nested 'result.error' field (from node_executor.py)
                        elif isinstance(execution_result.get("result"), dict):
                            result_dict = execution_result["result"]
                            self.logger.info(
                                f"Processing result dictionary: {json.dumps(result_dict, indent=2)}"
                            )

                            # Extract error message from result.error
                            if "error" in result_dict:
                                error_message = result_dict.get("error")
                                self.logger.info(
                                    f"Found error in result dictionary: {error_message}"
                                )

                                # Include additional error details if available
                                if "data" in result_dict and isinstance(
                                    result_dict["data"], dict
                                ):
                                    error_details = result_dict["data"].get("detail")
                                    if error_details:
                                        error_message = (
                                            f"{error_message} - {error_details}"
                                        )
                                        self.logger.info(
                                            f"Added error details: {error_message}"
                                        )

                                # Include status code if available
                                if "status_code" in result_dict:
                                    status_code = result_dict.get("status_code")
                                    if (
                                        status_code
                                        and not str(status_code) in error_message
                                    ):
                                        error_message = f"{error_message} (Status Code: {status_code})"
                                        self.logger.info(
                                            f"Added status code: {error_message}"
                                        )

                        # Fallback if no specific error format is matched
                        else:
                            error_message = execution_result.get(
                                "result", "Unknown error occurred"
                            )
                            self.logger.info(
                                f"Using fallback error message: {error_message}"
                            )

                        if error_message:
                            self.logger.error(
                                f"Execution failed with error: {error_message}"
                            )
                            # Force an exception to be raised for any error
                            raise Exception(f"Tool execution error: {error_message}")
                        else:
                            self.logger.error(
                                f"Execution failed but no error message could be extracted: {json.dumps(execution_result, indent=2)}"
                            )
                            raise Exception(
                                f"Tool execution error: Unknown error occurred"
                            )

                    # Special handling for Components execution type
                    elif execution_type == "Components" and isinstance(
                        execution_result.get("result"), dict
                    ):
                        result_dict = execution_result["result"]
                        # Check if there's an error field in the result dict even if status is not 'error'
                        if "error" in result_dict:
                            error_message = result_dict.get("error")
                            self.logger.info(
                                f"Found error in Components result: {error_message}"
                            )

                            # Include additional error details if available
                            if "data" in result_dict and isinstance(
                                result_dict["data"], dict
                            ):
                                error_details = result_dict["data"].get("detail")
                                if error_details:
                                    error_message = f"{error_message} - {error_details}"

                            # Include status code if available
                            if "status_code" in result_dict:
                                status_code = result_dict.get("status_code")
                                if (
                                    status_code
                                    and not str(status_code) in error_message
                                ):
                                    error_message = (
                                        f"{error_message} (Status Code: {status_code})"
                                    )

                            self.logger.error(
                                f"Components execution failed with error: {error_message}"
                            )
                            raise Exception(f"Tool execution error: {error_message}")

                # Wrap execution result in the expected structure for handle mapping compatibility
                wrapped_result = {
                    "transition_id": transition_id,
                    "node_id": node_id,
                    "tool_name": tool_name,
                    "result": {"result": execution_result},
                    "status": "completed",
                    "timestamp": time.time(),
                }
                tool_results[tool_name] = wrapped_result

                if execution_type == "MCP":
                    output_schema = node_tool_info.get("output_schema", {})

                    try:
                        execution_result_parsed = execution_result
                    except json.JSONDecodeError as e:
                        self.logger.error(f"JSONDecodeError DETECTED: {e}")
                        execution_result_parsed = []

                    formatted_result = format_execution_result(
                        output_schema, execution_result_parsed
                    )
                    # Log semantic type inclusion for debugging
                    self.logger.debug(
                        f"Formatted result with semantic types: {formatted_result}"
                    )
                else:
                    formatted_result = execution_result

                if self.result_callback:
                    try:
                        result_info["result"] = formatted_result
                        result_info["status"] = "completed"
                        result_info["message"] = "Transition Result received."
                        result_info["approval_required"] = (
                            True if approval_required and self.approval else False
                        )
                        await self.result_callback(result_info)
                        if approval_required and self.approval:
                            self.state_manager.workflow_paused = True
                            self.logger.info(
                                f"Transition '{transition_id}' completed. Workflow paused, waiting for approval..."
                            )
                            result_info["result"] = (
                                "Workflow paused, waiting for approval..."
                            )
                            result_info["message"] = (
                                "Workflow paused, waiting for approval..."
                            )
                            result_info["status"] = "paused"
                            await self.result_callback(result_info)
                            self.state_manager.transitions_waiting_for_approval.append(
                                transition_id
                            )
                            self._pause_event.clear()
                            await self._pause_event.wait()
                            self.logger.info(
                                f"Workflow resumed after approval for transition '{transition_id}'."
                            )
                            del self.state_manager.transitions_waiting_for_approval[
                                transition_id
                            ]
                            self.state_manager.workflow_paused = False
                    except Exception as callback_error:
                        self.logger.error(
                            f"Result callback function raised an exception: {callback_error}"
                        )
                if action_type == "regenerate":
                    return []

            except Exception as e:
                self.logger.error(
                    f"Tool execution failed for tool '{tool_name}' (tool_id: {tool_id}) in node '{node_id}' of transition '{transition_id}': {str(e)}"
                    + traceback.format_exc()
                )
                error_message = f"[ERROR] Tool Execution Failed with error: {str(e)}"
                tool_results[tool_name] = error_message
                result_info["result"] = error_message
                result_info["message"] = "Transition faced an error during execution."
                result_info["status"] = "failed"
                if self.result_callback:
                    await self.result_callback(result_info)
                raise Exception(f"Tool execution error: {error_message}")

        # Handle loop completion specially
        if execution_type == "loop":
            # For loop transitions, use special completion handling
            loop_next_transitions = await self._handle_loop_completion(
                execution_result, transition_id
            )
            return loop_next_transitions

        # Standard completion handling for non-loop transitions
        self.state_manager.mark_transition_completed(transition_id, tool_results)

        # Notify completion callbacks for loop body chain integration
        await self._notify_transition_completion(transition_id, execution_result)

        chosen_next_transitions = []
        conditional_routing = transition.get("conditional_routing")
        conditional_nodes = set()

        # Handle new component-based conditional routing first
        component_routing_transitions = await self._handle_transition_routing(
            transition, execution_result
        )
        if component_routing_transitions:
            self.logger.info(
                f"Component-based routing returned transitions: {component_routing_transitions}"
            )
            chosen_next_transitions.extend(component_routing_transitions)

        # Legacy embedded routing is no longer supported
        elif conditional_routing:
            self.logger.warning(
                f"Legacy embedded conditional routing detected in transition {transition_id}. "
                "This is no longer supported. Please use conditional components instead."
            )

        transition_output_transitions = [
            config.get("to_transition_id")
            for config in output_data_configs
            if config.get("to_transition_id")
        ]
        final_next_transitions = set()

        # Add all transitions from routing evaluation (component or legacy)
        for transition_id in chosen_next_transitions:
            if transition_id:  # Ensure we don't add None values
                final_next_transitions.add(transition_id)

        # Add standard output transitions if they don't conflict with conditional routing
        for output_transition in transition_output_transitions:
            if output_transition not in conditional_nodes:
                final_next_transitions.add(output_transition)

        next_transitions.extend(list(final_next_transitions))

        return next_transitions

    async def _resolve_loop_config_parameters(
        self, loop_config: dict, transition_id: str
    ) -> dict:
        """
        Resolve variables in loop configuration using existing parameter resolution mechanisms.

        Args:
            loop_config: Raw loop configuration dictionary
            transition_id: Current transition ID for context

        Returns:
            dict: Loop configuration with resolved variables
        """
        self.logger.debug(
            f"🔄 Resolving loop config parameters for transition: {transition_id}"
        )

        # Create a deep copy to avoid modifying the original
        import copy

        resolved_config = copy.deepcopy(loop_config)

        try:
            # Resolve variables in iteration_source.data if it exists
            iteration_source = resolved_config.get("iteration_source", {})
            if iteration_source.get("data"):
                data = iteration_source["data"]

                # If data is a string with variables, resolve it
                if isinstance(data, str) and "${" in data and "}" in data:
                    resolved_data = (
                        await self.workflow_utils.resolve_variable_reference(
                            data, transition_id
                        )
                    )
                    iteration_source["data"] = resolved_data
                    self.logger.debug(
                        f"🔧 Resolved iteration_source.data: {data} → {resolved_data}"
                    )

                # If data is a list, resolve each item that contains variables
                elif isinstance(data, list):
                    resolved_list = []
                    for item in data:
                        if isinstance(item, str) and "${" in item and "}" in item:
                            resolved_item = (
                                await self.workflow_utils.resolve_variable_reference(
                                    item, transition_id
                                )
                            )
                            resolved_list.append(resolved_item)
                        else:
                            resolved_list.append(item)
                    iteration_source["data"] = resolved_list
                    self.logger.debug(
                        f"🔧 Resolved iteration_source.data list with {len(resolved_list)} items"
                    )

                # If data is a dict (for range type), resolve individual values
                elif isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, str) and "${" in value and "}" in value:
                            resolved_value = (
                                await self.workflow_utils.resolve_variable_reference(
                                    value, transition_id
                                )
                            )
                            data[key] = resolved_value
                            self.logger.debug(
                                f"🔧 Resolved iteration_source.data.{key}: {value} → {resolved_value}"
                            )

            # Resolve any other string values in the config that contain variables
            await self._resolve_nested_variables(resolved_config, transition_id)

            self.logger.debug(
                f"✅ Loop config parameter resolution completed for transition: {transition_id}"
            )
            return resolved_config

        except Exception as e:
            self.logger.error(
                f"❌ Failed to resolve loop config parameters for transition {transition_id}: {str(e)}"
            )
            # Return original config if resolution fails
            return loop_config

    async def _resolve_nested_variables(
        self, obj: any, transition_id: str, path: str = ""
    ) -> None:
        """
        Recursively resolve variables in nested dictionaries and lists.

        Args:
            obj: Object to resolve variables in (dict, list, or primitive)
            transition_id: Current transition ID for context
            path: Current path in the object (for logging)
        """
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                if isinstance(value, str) and "${" in value and "}" in value:
                    try:
                        resolved_value = (
                            await self.workflow_utils.resolve_variable_reference(
                                value, transition_id
                            )
                        )
                        obj[key] = resolved_value
                        self.logger.debug(
                            f"🔧 Resolved {current_path}: {value} → {resolved_value}"
                        )
                    except Exception as e:
                        self.logger.warning(
                            f"⚠️ Failed to resolve variable at {current_path}: {str(e)}"
                        )
                elif isinstance(value, (dict, list)):
                    await self._resolve_nested_variables(
                        value, transition_id, current_path
                    )
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                if isinstance(item, str) and "${" in item and "}" in item:
                    try:
                        resolved_item = (
                            await self.workflow_utils.resolve_variable_reference(
                                item, transition_id
                            )
                        )
                        obj[i] = resolved_item
                        self.logger.debug(
                            f"🔧 Resolved {current_path}: {item} → {resolved_item}"
                        )
                    except Exception as e:
                        self.logger.warning(
                            f"⚠️ Failed to resolve variable at {current_path}: {str(e)}"
                        )
                elif isinstance(item, (dict, list)):
                    await self._resolve_nested_variables(
                        item, transition_id, current_path
                    )

    async def _handle_loop_completion(
        self, execution_result: Any, transition_id: str
    ) -> List[str]:
        """
        Handle loop completion and determine next transitions for new loop architecture.

        Args:
            execution_result: Result from loop execution (dual output structure)
            transition_id: ID of the completed loop transition

        Returns:
            List of next transition IDs to execute
        """
        self.logger.info(f"🔄 Handling loop completion for transition: {transition_id}")

        try:
            # Store loop result in state manager
            self.state_manager.mark_transition_completed(
                transition_id, execution_result
            )

            # Notify completion callbacks for loop body chain integration
            await self._notify_transition_completion(transition_id, execution_result)

            # Get the transition configuration to find next transitions
            transition = self.transitions_by_id.get(transition_id)
            if not transition:
                self.logger.error(
                    f"Transition {transition_id} not found for completion handling"
                )
                return []

            # New architecture: Handle dual output routing
            next_transitions = []

            # Check if this is the new loop architecture with dual outputs
            if isinstance(execution_result, dict) and "output_type" in execution_result:
                output_type = execution_result.get("output_type")

                if output_type == "exit_output":
                    # This is the final exit output - route to exit transitions
                    next_transitions = self._get_exit_output_transitions(transition)
                    self.logger.info(
                        f"🚪 Exit output detected, routing to exit transitions: {next_transitions}"
                    )
                elif output_type == "iteration_output":
                    # This is iteration output - should route to connected processing nodes
                    # But since this is completion, we shouldn't get iteration outputs here
                    self.logger.warning(
                        f"⚠️ Received iteration output in completion handler - this may indicate an issue"
                    )
                    next_transitions = []
            else:
                # Legacy format or standard completion - extract from output_data configurations
                node_info = transition.get("node_info", {})
                output_data_configs = node_info.get("output_data", [])

                # Get loop body transitions to filter them out from next transitions
                loop_config = transition.get("loop_config", {})
                loop_body_transitions = loop_config.get("loop_body_transitions", [])

                # Check for new schema format (loop_body_configuration)
                loop_body_config = loop_config.get("loop_body_configuration", {})
                entry_transitions = loop_body_config.get("entry_transitions", [])
                exit_transitions = loop_body_config.get("exit_transitions", [])

                # If we have explicit configuration in new format, use it
                if entry_transitions or exit_transitions:
                    # Combine entry and exit transitions as loop body transitions
                    loop_body_transitions = list(set(entry_transitions + exit_transitions))
                    self.logger.info(
                        f"🔍 Using explicit loop body configuration: entry={entry_transitions}, exit={exit_transitions}"
                    )
                # If loop_body_transitions is not explicitly configured, auto-detect them
                elif not loop_body_transitions:
                    loop_body_transitions = self._auto_detect_loop_body_transitions(
                        output_data_configs
                    )
                    self.logger.info(
                        f"🔍 Auto-detected loop body transitions: {loop_body_transitions}"
                    )

                self.logger.debug(
                    f"🔍 Loop completion filtering - Loop config: {loop_config}"
                )
                self.logger.debug(
                    f"🔍 Loop body transitions to filter: {loop_body_transitions}"
                )
                self.logger.debug(
                    f"🔍 Output data configs: {output_data_configs}"
                )

                for config in output_data_configs:
                    to_transition_id = config.get("to_transition_id")
                    if to_transition_id:
                        # Only add transitions that are NOT loop body transitions
                        # Loop body transitions should have been executed internally by the loop executor
                        if to_transition_id not in loop_body_transitions:
                            next_transitions.append(to_transition_id)
                            self.logger.debug(
                                f"✅ Adding exit transition '{to_transition_id}' to next transitions"
                            )
                        else:
                            self.logger.debug(
                                f"🚫 Skipping loop body transition '{to_transition_id}' - "
                                f"should have been executed internally by loop executor"
                            )

                # Check for exit_transition in loop config (legacy support)
                exit_transition = loop_config.get("exit_transition")
                if exit_transition and exit_transition not in next_transitions:
                    next_transitions.append(exit_transition)

            self.logger.info(
                f"✅ Loop completion handled for {transition_id}. Next transitions: {next_transitions}"
            )

            # Log loop execution statistics if available
            self._log_loop_statistics(execution_result)

            return next_transitions

        except Exception as e:
            self.logger.error(
                f"❌ Failed to handle loop completion for {transition_id}: {str(e)}"
            )
            return []

    def _auto_detect_loop_body_transitions(self, output_data_configs: List[dict]) -> List[str]:
        """
        Auto-detect loop body transitions based on handle mappings.

        Loop body transitions typically:
        - Use 'current_item' handle (individual iteration data)
        - Have iteration-related result paths

        Exit transitions typically:
        - Use 'final_results' or 'aggregated_results' handles
        - Have exit/final-related result paths

        Args:
            output_data_configs: List of output data configurations

        Returns:
            List of transition IDs that are likely loop body transitions
        """
        loop_body_transitions = []

        for config in output_data_configs:
            to_transition_id = config.get("to_transition_id")
            if not to_transition_id:
                continue

            # Check handle mappings to determine if this is a loop body transition
            output_handle_registry = config.get("output_handle_registry", {})
            handle_mappings = output_handle_registry.get("handle_mappings", [])

            is_loop_body = False
            is_exit = False

            for mapping in handle_mappings:
                result_path = mapping.get("result_path", "").lower()
                handle_name = mapping.get("handle_name", "").lower()

                # Check for loop body indicators
                if (
                    "current_item" in result_path or
                    "current_item" in handle_name or
                    "iteration" in result_path or
                    "item" in handle_name
                ):
                    is_loop_body = True

                # Check for exit indicators
                if (
                    "final" in result_path or
                    "aggregated" in result_path or
                    "exit" in result_path or
                    "final_results" in handle_name or
                    "aggregated_results" in handle_name
                ):
                    is_exit = True

            # If it looks like a loop body transition and not an exit transition
            if is_loop_body and not is_exit:
                loop_body_transitions.append(to_transition_id)
                self.logger.debug(
                    f"🔍 Auto-detected loop body transition: {to_transition_id} "
                    f"(has current_item/iteration indicators)"
                )
            elif is_exit:
                self.logger.debug(
                    f"🔍 Detected exit transition: {to_transition_id} "
                    f"(has final/aggregated indicators)"
                )
            else:
                # If unclear, assume it's an exit transition to be safe
                self.logger.debug(
                    f"🔍 Unclear transition type: {to_transition_id} "
                    f"(treating as exit transition)"
                )

        return loop_body_transitions

    def _get_exit_output_transitions(self, transition: dict) -> List[str]:
        """
        Get transitions that should be executed when loop exits.

        Args:
            transition: Loop transition configuration

        Returns:
            List of transition IDs for exit output routing
        """
        next_transitions = []

        # Extract from output_data configurations - look for exit output mappings
        node_info = transition.get("node_info", {})
        output_data_configs = node_info.get("output_data", [])

        for config in output_data_configs:
            # Check if this output config is for exit output
            output_handle_registry = config.get("output_handle_registry", {})
            handle_mappings = output_handle_registry.get("handle_mappings", [])

            # Look for exit-related handles
            for mapping in handle_mappings:
                result_path = mapping.get("result_path", "")
                if (
                    "aggregated" in result_path
                    or "exit" in result_path
                    or "final" in result_path
                ):
                    to_transition_id = config.get("to_transition_id")
                    if to_transition_id and to_transition_id not in next_transitions:
                        next_transitions.append(to_transition_id)
                        break

        return next_transitions

    def _log_loop_statistics(self, execution_result: Any) -> None:
        """
        Log loop execution statistics.

        Args:
            execution_result: Result from loop execution
        """
        try:
            if isinstance(execution_result, dict):
                if "loop_metadata" in execution_result:
                    metadata = execution_result["loop_metadata"]
                    total_iterations = metadata.get("total_iterations", 0)
                    completed_iterations = metadata.get("completed_iterations", 0)
                    failed_iterations = metadata.get("failed_iterations", 0)

                    self.logger.info(
                        f"📊 Loop statistics: {completed_iterations}/{total_iterations} completed, "
                        f"{failed_iterations} failed"
                    )
                elif "aggregated_results" in execution_result:
                    results = execution_result["aggregated_results"]
                    if isinstance(results, list):
                        self.logger.info(
                            f"📊 Loop executed {len(results)} iterations successfully"
                        )
            elif isinstance(execution_result, list):
                self.logger.info(
                    f"📊 Loop executed {len(execution_result)} iterations successfully"
                )
        except Exception as e:
            self.logger.debug(f"Could not extract loop statistics: {str(e)}")

    async def _resolve_tool_parameters_universally(
        self,
        node_tool_info: dict,
        input_data_configs: list,
        transition_id: str,
        tool_params_config: dict,
        transition: dict,
    ) -> dict:
        """
        Universal tool parameter resolution using handle-based system.

        This method uses the new handle-based data propagation system to resolve
        tool parameters without hardcoded patterns or placeholder interpretation.

        Args:
            node_tool_info: Tool information from node definition
            input_data_configs: Input data configurations with handle mappings
            transition_id: Current transition ID
            tool_params_config: Tool parameter configuration
            transition: Complete transition object with result_resolution metadata

        Returns:
            dict: Resolved tool parameters
        """
        self.logger.debug(
            f"🔧 Starting universal parameter resolution for transition: {transition_id}"
        )

        # Check if we have result_resolution metadata for enhanced resolution
        result_resolution = transition.get("result_resolution", {})
        if result_resolution:
            self.logger.debug(
                f"📊 Using result_resolution metadata: {result_resolution.get('node_type', 'unknown')}"
            )

            # Use enhanced handle-based resolution with result_resolution metadata
            return await self._resolve_with_result_resolution(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
                result_resolution,
            )
        else:
            self.logger.debug(
                "⚠️ No result_resolution metadata found, falling back to standard handle resolution"
            )

            # Fallback to standard handle-based resolution
            return await self.workflow_utils._format_tool_parameters(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
            )

    async def _resolve_with_result_resolution(
        self,
        node_tool_info: dict,
        input_data_configs: list,
        transition_id: str,
        tool_params_config: dict,
        result_resolution: dict,
    ) -> dict:
        """
        Enhanced parameter resolution using result_resolution metadata.

        This method uses the complete result_resolution system to provide
        the most accurate parameter resolution possible.

        Args:
            node_tool_info: Tool information from node definition
            input_data_configs: Input data configurations with handle mappings
            transition_id: Current transition ID
            tool_params_config: Tool parameter configuration
            result_resolution: Result resolution metadata from transition

        Returns:
            dict: Resolved tool parameters with enhanced accuracy
        """
        node_type = result_resolution.get("node_type", "unknown")
        self.logger.debug(f"🎯 Enhanced parameter resolution for {node_type} node")

        # Special handling for conditional components
        if node_type == "conditional" or "conditional" in transition_id.lower():
            return await self._resolve_conditional_component_parameters(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
                result_resolution,
            )

        # Collect all previous results
        all_previous_results = {}
        if input_data_configs:
            for input_data_config in input_data_configs:
                from_transition_id = input_data_config.get("from_transition_id")
                if from_transition_id:
                    result_from_dependency = self.state_manager.get_transition_result(
                        from_transition_id
                    )
                    if result_from_dependency:
                        all_previous_results[from_transition_id] = (
                            result_from_dependency
                        )
                        self.logger.debug(
                            f"📥 Collected results from transition {from_transition_id}"
                        )

        # If no previous results, convert current params to dict format
        if not all_previous_results:
            self.logger.debug("📝 No previous results found, using static parameters")
            return self.workflow_utils._convert_params_to_dict(tool_params_config)

        # Extract handle mappings from input data configs
        handle_mappings = self.workflow_utils._extract_handle_mappings(
            input_data_configs
        )

        if not handle_mappings:
            self.logger.debug(
                "🔄 No handle mappings found, falling back to standard resolution"
            )
            return await self.workflow_utils._format_tool_parameters(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
            )

        # Validate handle mapping compatibility
        validation_report = self.workflow_utils.validate_handle_mapping_compatibility(
            handle_mappings, all_previous_results
        )

        self.logger.info(
            f"🔍 Handle validation: {validation_report['overall_compatibility']} "
            f"({validation_report['compatible_mappings']}/{validation_report['total_mappings']} compatible)"
        )

        # Create universal parameter mapping
        parameter_mapping = self.workflow_utils.create_universal_parameter_mapping(
            handle_mappings, all_previous_results
        )

        resolved_parameters = parameter_mapping["resolved_parameters"]
        mapping_metadata = parameter_mapping["mapping_metadata"]

        # Log detailed mapping results
        self.logger.info(
            f"🎯 Parameter mapping complete: {mapping_metadata['successful_mappings']}/{mapping_metadata['total_mappings']} successful"
        )

        # If we have failed mappings, log details for debugging
        if mapping_metadata["failed_mappings"] > 0:
            failed_details = [
                detail
                for detail in mapping_metadata["mapping_details"]
                if detail["status"] in ["failed", "error"]
            ]
            for detail in failed_details:
                self.logger.warning(
                    f"❌ Failed mapping: {detail['source_handle_id']} → {detail['target_handle_id']} "
                    f"(Error: {detail.get('error', 'Unknown')})"
                )

        # Merge with static parameters that don't have handle mappings
        current_params_dict = self.workflow_utils._convert_params_to_dict(
            tool_params_config
        )
        for param_name, param_value in current_params_dict.items():
            if param_name not in resolved_parameters:
                # Only include if it's not a placeholder (no ${...} pattern)
                if not (
                    isinstance(param_value, str)
                    and "${" in param_value
                    and "}" in param_value
                ):
                    resolved_parameters[param_name] = param_value
                    self.logger.debug(
                        f"📌 Added static parameter: {param_name} = {param_value}"
                    )

        self.logger.debug(f"✅ Final resolved parameters: {resolved_parameters}")
        return resolved_parameters

    async def _resolve_conditional_component_parameters(
        self,
        node_tool_info: dict,
        input_data_configs: list,
        transition_id: str,
        tool_params_config: dict,
        result_resolution: dict,
    ) -> dict:
        """
        Special parameter resolution for conditional components.

        Conditional components require specific parameters:
        - conditions: List of condition configurations
        - node_output: Output from previous node
        - default_transition: Fallback transition ID
        - global_context: Global context variables (optional)

        Args:
            node_tool_info: Tool information from node definition
            input_data_configs: Input data configurations with handle mappings
            transition_id: Current transition ID
            tool_params_config: Tool parameter configuration
            result_resolution: Result resolution metadata from transition

        Returns:
            dict: Resolved parameters for conditional component
        """
        self.logger.debug(
            f"🔀 Resolving conditional component parameters for {transition_id}"
        )

        # Start with base parameters from tool config
        resolved_params = self.workflow_utils._convert_params_to_dict(
            tool_params_config
        )

        # Collect previous node output
        node_output = None
        if input_data_configs:
            for input_data_config in input_data_configs:
                from_transition_id = input_data_config.get("from_transition_id")
                if from_transition_id:
                    result_from_dependency = self.state_manager.get_transition_result(
                        from_transition_id
                    )
                    if result_from_dependency:
                        # Extract the actual output data from the result
                        if isinstance(result_from_dependency, dict):
                            # Try to get the actual result data
                            if "result" in result_from_dependency:
                                inner_result = result_from_dependency["result"]
                                if (
                                    isinstance(inner_result, dict)
                                    and "result" in inner_result
                                ):
                                    # Double-nested result structure
                                    node_output = inner_result["result"]
                                elif (
                                    isinstance(inner_result, dict)
                                    and "output_data" in inner_result
                                ):
                                    # Standard output_data structure
                                    node_output = inner_result["output_data"]
                                else:
                                    # Use the inner result directly
                                    node_output = inner_result
                            else:
                                # Use the result directly
                                node_output = result_from_dependency
                        else:
                            node_output = result_from_dependency

                        self.logger.debug(
                            f"📥 Extracted node_output from {from_transition_id}: {node_output}"
                        )
                        break

        # Set required parameters for conditional component
        resolved_params["node_output"] = node_output

        # Ensure conditions are present (should come from tool_params_config)
        if "conditions" not in resolved_params:
            self.logger.warning(
                f"⚠️ No conditions found in tool parameters for conditional component {transition_id}"
            )
            resolved_params["conditions"] = []

        # Ensure default_transition is present
        if "default_transition" not in resolved_params:
            self.logger.warning(
                f"⚠️ No default_transition found in tool parameters for conditional component {transition_id}"
            )
            resolved_params["default_transition"] = "default"

        # Add global_context if not present
        if "global_context" not in resolved_params:
            resolved_params["global_context"] = {}

        self.logger.debug(
            f"✅ Conditional component parameters resolved: {resolved_params}"
        )
        return resolved_params

    def _get_executor_for_type(self, execution_type):
        """
        Returns the appropriate executor based on the execution type.
        """
        # MCP server executor types
        if execution_type == "MCP":
            return self.tool_executor

        # Node executor types
        elif execution_type == "Components":
            if self.node_executor:
                return self.node_executor
            else:
                self.logger.warning(
                    f"Node executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor

        # Agent executor types
        elif execution_type == "agent":
            if self.agent_executor:
                return self.agent_executor
            else:
                self.logger.warning(
                    f"Agent executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor

        # Loop executor types
        elif execution_type == "loop":
            try:
                # Create a new LoopExecutor instance for this execution
                loop_executor = LoopExecutor(
                    state_manager=self.state_manager,
                    workflow_utils=self.workflow_utils,
                    result_callback=self.result_callback,
                    transitions_by_id=self.transitions_by_id,
                    nodes=self.nodes,
                    transition_handler=self,
                    user_id=self.user_id,
                )

                # Set the orchestration engine reference for proper coordination
                if hasattr(self, 'orchestration_engine') and self.orchestration_engine:
                    loop_executor.set_orchestration_engine(self.orchestration_engine)
                    self.logger.debug(f"🔗 Set orchestration engine for loop executor")
                return loop_executor
            except Exception as e:
                self.logger.error(
                    f"Failed to instantiate LoopExecutor for execution type: {execution_type}. Error: {str(e)}"
                )
                self.logger.warning(
                    f"Loop executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor
        else:
            self.logger.warning(
                f"Unknown execution type: {execution_type}. Falling back to MCP executor."
            )
            return self.tool_executor

    async def _handle_reflection_logic(self, transition: dict) -> list[str]:
        """
        Handles reflection logic.
        """
        reflection_info = transition.get("reflection", {})
        next_transition_candidates = []

        if not reflection_info:
            next_transition_candidates = (
                await self._execute_standard_or_reflection_transition(transition)
            )
            return next_transition_candidates

        iteration_count = reflection_info.get("iteration_count", 0) + 1
        max_iterations = reflection_info.get("max_iterations", 1)
        reflection_info["iteration_count"] = iteration_count

        self.logger.info(
            f"[REFLECTION] transition '{transition['id']}' iteration {iteration_count}/{max_iterations}."
        )

        if iteration_count > max_iterations:
            self.logger.info(
                f"Max iterations {max_iterations} reached. Reflection ends."
            )
            next_transition_candidates = (
                await self._execute_standard_or_reflection_transition(transition)
            )
            return next_transition_candidates

        # Execute the reflection logic
        next_transition_candidates = (
            await self._execute_standard_or_reflection_transition(transition)
        )
        return next_transition_candidates

    def _resolve_next_transition(self, next_transition_candidates):
        """
        Resolve next transitions from transition candidates, prioritizing reflection transitions and then handling others based on closest sequence.
        Now directly handles a list of transition IDs as input (next_transition_candidates).
        Returns a list of ALL transition IDs to be executed next.
        Resolution logic:
        1. Prioritize reflection transitions.
        2. For non-reflection transitions, choose based on closest *higher* sequence number than current_transition.
        """
        if not next_transition_candidates:
            self.logger.info(
                "No next transition candidates provided for resolution. Returning empty list."
            )
            return []

        reflection_transitions = []
        non_reflection_transitions = []
        resolved_transition_ids = set()

        for transition_id in next_transition_candidates:
            if transition_id in resolved_transition_ids:
                continue

            transition = self.transitions_by_id.get(transition_id)
            if not transition:
                self.logger.warning(
                    f"Transition ID '{transition_id}' from candidates not found in transitions_by_id."
                )
                continue

            if transition["transition_type"] == "reflection":
                reflection_transitions.append(transition_id)
            else:
                non_reflection_transitions.append(transition_id)
            resolved_transition_ids.add(transition_id)

        prioritized_transitions = reflection_transitions + non_reflection_transitions
        self.logger.info(
            f"Resolved next transitions (direct transition IDs): {prioritized_transitions}"
        )
        return prioritized_transitions

    async def regenerate_transition(
        self, transition_id, action_type, server_params_override=None
    ):
        """
        Regenerates a specific transition (server) and its dependent transitions.
        Resets the state for the transition and re-executes it, then returns.
        Does NOT automatically resume the entire workflow execution flow.
        """
        if transition_id not in self.transitions_by_id:
            self.logger.error(
                f"Transition ID '{transition_id}' not found for regeneration: {transition_id}"
            )
            return False

        self.logger.info(f"Initiating regeneration for transition: {transition_id}")

        if action_type == "re-execute":
            # 1. Reset workflow state for the given transition and its dependents
            reset_success = self.state_manager.reset_to_transition(
                transition_id, self.transitions_by_id, self.dependency_map
            )
            if not reset_success:
                self.logger.error(
                    f"State reset failed for transition {transition_id}, regeneration cannot proceed."
                )
                return False

        # 2. Get the transition object
        transition_to_regenerate = self.transitions_by_id[transition_id]

        original_approval_flag = self.approval
        self.approval = False

        # 3. Execute the transition directly, passing server_params_override
        result = await self._execute_standard_or_reflection_transition(
            transition_to_regenerate,
            server_params_override=server_params_override,
            action_type="regenerate",
        )

        self.approval = original_approval_flag

        if result is not None:
            self.logger.info(f"Regeneration completed for transition: {transition_id}")
            return True
        else:
            self.logger.error(f"Regeneration failed for transition: {transition_id}")
            return False

    def _is_conditional_component_transition(self, transition: dict) -> bool:
        """
        Check if transition uses conditional component.

        Args:
            transition: Transition configuration dict

        Returns:
            True if transition uses conditional component, False otherwise
        """
        node_info = transition.get("node_info", {})
        tools_to_use = node_info.get("tools_to_use", [])

        return any(tool.get("tool_name") == "conditional" for tool in tools_to_use)

    async def _handle_conditional_component_result(
        self, execution_result: dict, transition: dict
    ) -> list[str]:
        """
        Handle conditional component execution result and determine next transitions.

        Args:
            execution_result: The execution result from conditional component
            transition: The transition configuration that was executed

        Returns:
            List of next transition IDs to execute
        """
        return await self.conditional_routing_handler.handle_conditional_result(
            execution_result, transition
        )

    async def _handle_transition_routing(
        self, transition: dict, execution_result: any
    ) -> list[str]:
        """
        Handle both legacy embedded routing and new component-based routing.

        Args:
            transition: Transition configuration dict
            execution_result: Result from transition execution

        Returns:
            List of next transition IDs to execute
        """
        transition_id = transition.get("id", "unknown")
        self.logger.debug(f"🔀 _handle_transition_routing called for {transition_id}")
        self.logger.debug(f"🔀 Execution result type: {type(execution_result)}")
        self.logger.debug(
            f"🔀 Execution result keys: {list(execution_result.keys()) if isinstance(execution_result, dict) else 'not dict'}"
        )

        # Check for legacy embedded routing (no longer supported)
        conditional_routing = transition.get("conditional_routing")
        if conditional_routing:
            self.logger.warning(
                f"🔀 Legacy embedded routing detected for {transition_id}. "
                "This is no longer supported. Please use conditional components instead."
            )
            return []

        # Check for new component-based routing
        is_conditional_result = (
            self.conditional_routing_handler.is_conditional_component_result(
                execution_result
            )
        )
        is_conditional_transition = self._is_conditional_component_transition(
            transition
        )

        self.logger.debug(f"🔀 Component routing check for {transition_id}:")
        self.logger.debug(f"🔀   - is_conditional_result: {is_conditional_result}")
        self.logger.debug(
            f"🔀   - is_conditional_transition: {is_conditional_transition}"
        )

        if is_conditional_result and is_conditional_transition:
            self.logger.info(
                f"🔀 Processing component-based conditional routing for {transition_id}"
            )
            # New component-based routing
            result = await self._handle_conditional_component_result(
                execution_result, transition
            )
            self.logger.info(f"🔀 Component routing returned: {result}")
            return result

        # No routing logic found
        self.logger.debug(
            f"🔀 No routing logic found for {transition_id}, returning empty list"
        )
        return []
