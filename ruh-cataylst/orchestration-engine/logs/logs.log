2025-06-28 03:03:32 - Main - INFO - Starting Server
2025-06-28 03:03:32 - Main - INFO - Connection at: **************:9092
2025-06-28 03:03:32 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 03:03:32 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 03:03:32 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 03:03:32 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 03:03:32 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 03:03:34 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 03:03:34 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 03:03:36 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 03:03:38 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 03:03:38 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 03:03:40 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 03:03:41 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 03:03:41 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 03:03:43 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 03:03:43 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 03:03:44 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 03:03:44 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 03:03:44 - RedisEventListener - INFO - Redis event listener started
2025-06-28 03:03:44 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 03:03:44 - StateManager - DEBUG - Using provided database connections
2025-06-28 03:03:44 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 03:03:44 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 03:03:44 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 03:03:45 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 03:03:45 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 03:03:45 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 03:03:45 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 03:03:45 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 03:03:46 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 03:03:46 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 03:03:48 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 03:03:48 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 03:03:48 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 03:04:02 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 03:04:08 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 03:04:08 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 03:04:08 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 03:04:15 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 03:04:15 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 03:04:15 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 03:04:21 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 03:04:21 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 03:04:41 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 03:04:41 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 03:04:42 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 03:04:42 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 03:04:56 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:74c56034-91a3-4522-88b7-07e638c4b3ea', 'data': b'expired'}
2025-06-28 03:04:56 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:74c56034-91a3-4522-88b7-07e638c4b3ea'
2025-06-28 03:04:56 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:74c56034-91a3-4522-88b7-07e638c4b3ea
2025-06-28 03:04:56 - RedisEventListener - DEBUG - Extracted key: workflow_state:74c56034-91a3-4522-88b7-07e638c4b3ea
2025-06-28 03:04:56 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 03:04:56 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 03:04:56 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 74c56034-91a3-4522-88b7-07e638c4b3ea
2025-06-28 03:04:56 - RedisEventListener - INFO - Archiving workflow state for workflow: 74c56034-91a3-4522-88b7-07e638c4b3ea
2025-06-28 03:05:00 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 03:05:01 - PostgresManager - DEBUG - Updated workflow state for correlation_id: temp_initialization
2025-06-28 03:05:01 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: temp_initialization
2025-06-28 03:05:10 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1003
2025-06-28 03:05:10 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751060110, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': '1', 'transition_id': 'CombineTextComponent-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 03:05:10 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 03:05:10 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 03:05:11 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 03:05:11 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow each loop testing retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "each loop testing",
    "description": "each_loop_testing",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/6ba8123c-48d0-42ee-a793-63df9f6f013e.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/6956a753-e759-4553-a8ec-75b86012e846.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "string",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-27T21:22:45.891280",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751052569332"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-28 03:05:11 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 03:05:11 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 03:05:11 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 03:05:11 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 03:05:11 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 03:05:11 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 03:05:13 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 03:05:13 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 03:05:13 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 03:05:13 - StateManager - DEBUG - Using provided database connections
2025-06-28 03:05:13 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 03:05:13 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 03:05:13 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 03:05:14 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 03:05:14 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 03:05:14 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-CombineTextComponent-*************']
2025-06-28 03:05:14 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751052569332: ['transition-LoopNode-*************']
2025-06-28 03:05:14 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 03:05:14 - StateManager - INFO - Built dependency map for 4 transitions
2025-06-28 03:05:14 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-CombineTextComponent-*************']
2025-06-28 03:05:14 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751052569332 depends on: ['transition-LoopNode-*************']
2025-06-28 03:05:14 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 03:05:14 - MCPToolExecutor - DEBUG - Set correlation ID to: f23d086f-aae9-4de8-9d1f-916862c870cb
2025-06-28 03:05:14 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f23d086f-aae9-4de8-9d1f-916862c870cb in tool_executor
2025-06-28 03:05:14 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 03:05:14 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 03:05:14 - NodeExecutor - DEBUG - Set correlation ID to: f23d086f-aae9-4de8-9d1f-916862c870cb
2025-06-28 03:05:14 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f23d086f-aae9-4de8-9d1f-916862c870cb in node_executor
2025-06-28 03:05:14 - AgentExecutor - DEBUG - Set correlation ID to: f23d086f-aae9-4de8-9d1f-916862c870cb
2025-06-28 03:05:14 - EnhancedWorkflowEngine - DEBUG - Set correlation_id f23d086f-aae9-4de8-9d1f-916862c870cb in agent_executor
2025-06-28 03:05:14 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 03:05:14 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 03:05:14 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 03:05:14 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: f23d086f-aae9-4de8-9d1f-916862c870cb
2025-06-28 03:05:14 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: f23d086f-aae9-4de8-9d1f-916862c870cb
2025-06-28 03:05:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 03:05:14 - StateManager - INFO - Workflow initialized with initial transition: transition-CombineTextComponent-*************
2025-06-28 03:05:14 - StateManager - DEBUG - State: pending={'transition-CombineTextComponent-*************'}, waiting=set(), completed=set()
2025-06-28 03:05:14 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-CombineTextComponent-*************
2025-06-28 03:05:14 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-28 03:05:15 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f23d086f-aae9-4de8-9d1f-916862c870cb'
2025-06-28 03:05:15 - RedisManager - DEBUG - Set key 'workflow_state:f23d086f-aae9-4de8-9d1f-916862c870cb' with TTL of 600 seconds
2025-06-28 03:05:15 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f23d086f-aae9-4de8-9d1f-916862c870cb. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 03:05:15 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 03:05:15 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 03:05:15 - StateManager - INFO - Cleared 1 pending transitions: {'transition-CombineTextComponent-*************'}
2025-06-28 03:05:15 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 03:05:15 - StateManager - INFO - Terminated: False
2025-06-28 03:05:15 - StateManager - INFO - Pending transitions (0): []
2025-06-28 03:05:15 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 03:05:15 - StateManager - INFO - Completed transitions (0): []
2025-06-28 03:05:15 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 03:05:15 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 03:05:15 - StateManager - INFO - Workflow status: inactive
2025-06-28 03:05:15 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 03:05:15 - StateManager - INFO - Workflow status: inactive
2025-06-28 03:05:15 - StateManager - INFO - Workflow paused: False
2025-06-28 03:05:15 - StateManager - INFO - ==============================
2025-06-28 03:05:15 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 03:05:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 03:05:15 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=initial, execution_type=Components)
2025-06-28 03:05:15 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 03:05:15 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 03:05:15 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 03:05:15 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 03:05:15 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 03:05:15 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 03:05:15 - TransitionHandler - DEBUG - tool Parameters: {'main_input': '1', 'num_additional_inputs': '0'}
2025-06-28 03:05:15 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': '1', 'num_additional_inputs': '0'}
2025-06-28 03:05:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 03:05:15 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 3c432562-6d6d-4593-9145-7a83125c6c43) using provided producer.
2025-06-28 03:05:15 - NodeExecutor - DEBUG - Added correlation_id f23d086f-aae9-4de8-9d1f-916862c870cb to payload
2025-06-28 03:05:15 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 03:05:15 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': '1', 'num_additional_inputs': '0'}, 'request_id': '3c432562-6d6d-4593-9145-7a83125c6c43', 'correlation_id': 'f23d086f-aae9-4de8-9d1f-916862c870cb', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 03:05:15 - NodeExecutor - DEBUG - Request 3c432562-6d6d-4593-9145-7a83125c6c43 sent successfully using provided producer.
2025-06-28 03:05:15 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 3c432562-6d6d-4593-9145-7a83125c6c43...
2025-06-28 03:05:15 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1003, corr_id: f23d086f-aae9-4de8-9d1f-916862c870cb
2025-06-28 03:05:16 - NodeExecutor - DEBUG - Result consumer received message: Offset=848
2025-06-28 03:05:16 - NodeExecutor - DEBUG - Received valid result for request_id 3c432562-6d6d-4593-9145-7a83125c6c43
2025-06-28 03:05:16 - NodeExecutor - INFO - Result received for request 3c432562-6d6d-4593-9145-7a83125c6c43.
2025-06-28 03:05:16 - TransitionHandler - INFO - Execution result from Components executor: "1"
2025-06-28 03:05:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '1', 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 03:05:16 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '1'}, 'status': 'completed', 'timestamp': 1751060116.822633}}
2025-06-28 03:05:17 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 03:05:17 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 03:05:17 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 03:05:17 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 03:05:17 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************'}
2025-06-28 03:05:17 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 03:05:17 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 03:05:17 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 03:05:17 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 03:05:17 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 03:05:17 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 03:05:17 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 03:05:17 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 03:05:17 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.31 seconds
2025-06-28 03:05:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'result': 'Completed transition in 2.31 seconds', 'message': 'Transition completed in 2.31 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 03:05:17 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-06-28 03:05:17 - EnhancedWorkflowEngine - INFO - Transition transition-CombineTextComponent-************* completed successfully: 1 next transitions
2025-06-28 03:05:17 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-06-28 03:05:17 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-06-28 03:05:17 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 03:05:18 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:f23d086f-aae9-4de8-9d1f-916862c870cb'
2025-06-28 03:05:18 - RedisManager - DEBUG - Set key 'workflow_state:f23d086f-aae9-4de8-9d1f-916862c870cb' with TTL of 600 seconds
2025-06-28 03:05:18 - StateManager - INFO - Workflow state saved to Redis for workflow ID: f23d086f-aae9-4de8-9d1f-916862c870cb. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 03:05:18 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 03:05:18 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 03:05:18 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 03:05:18 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 03:05:18 - StateManager - INFO - Terminated: False
2025-06-28 03:05:18 - StateManager - INFO - Pending transitions (0): []
2025-06-28 03:05:18 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 03:05:18 - StateManager - INFO - Completed transitions (1): ['transition-CombineTextComponent-*************']
2025-06-28 03:05:18 - StateManager - INFO - Results stored for 1 transitions
2025-06-28 03:05:18 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 03:05:18 - StateManager - INFO - Workflow status: inactive
2025-06-28 03:05:18 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 03:05:18 - StateManager - INFO - Workflow status: inactive
2025-06-28 03:05:18 - StateManager - INFO - Workflow paused: False
2025-06-28 03:05:18 - StateManager - INFO - ==============================
2025-06-28 03:05:18 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 03:05:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-06-28 03:05:18 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-06-28 03:05:18 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 03:05:18 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 03:05:18 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 03:05:18 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 03:05:18 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 03:05:19 - StateManager - DEBUG - Retrieved result for transition transition-CombineTextComponent-************* from Redis
2025-06-28 03:05:19 - StateManager - DEBUG - Detected wrapped result structure for transition transition-CombineTextComponent-*************, extracting data
2025-06-28 03:05:19 - StateManager - DEBUG - Extracted double-nested result data for transition transition-CombineTextComponent-*************
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-CombineTextComponent-*************
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-CombineTextComponent-************* (total: 1)
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': '1'}
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: 1
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - Found result.result: 1 (type: <class 'str'>)
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-28 03:05:19 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 03:05:19 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': '1'}
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: 1
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - Found result.result: 1 (type: <class 'str'>)
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - ✅ Handle mapping success: result → start via path 'result': 1
2025-06-28 03:05:19 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 03:05:19 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-28 03:05:19 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-06-28 03:05:19 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 3
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = True
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = True
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = continue
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-06-28 03:05:19 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '1', 'source_type': 'number_range', 'batch_size': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 03:05:19 - TransitionHandler - DEBUG - tool Parameters: {'start': '1', 'source_type': 'number_range', 'batch_size': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 03:05:19 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '1', 'source_type': 'number_range', 'batch_size': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 03:05:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 03:05:19 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 03:05:19 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 03:05:19 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 03:05:19 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-06-28 03:05:19 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 03:05:20 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 03:05:20 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 03:05:20 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 03:05:20 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************'}
2025-06-28 03:05:20 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-06-28 03:05:20 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 03:05:21 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 03:05:21 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 03:05:21 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 03:05:21 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************', 'current_iteration'}
2025-06-28 03:05:21 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 248562.98398175, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 03:05:21 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 03:05:22 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 03:05:22 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 03:05:22 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 03:05:22 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 03:05:22 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 03:05:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-28 03:05:22 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 03:05:22 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 03:05:22 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 03:05:22 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 03:05:22 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 03:05:23 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 03:05:23 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 03:05:23 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 248562.98398175, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 03:05:23 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 03:05:23 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 248562.98398175, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 1
2025-06-28 03:05:23 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 03:05:23 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 03:05:23 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 4 fields (9 null/empty fields removed)
2025-06-28 03:05:23 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 03:05:23 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 03:05:23 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 03:05:23 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 03:05:23 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 03:05:23 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 03:05:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-28 03:05:23 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 5ef6984f-9ad4-47b0-8352-feae2d45b56e) using provided producer.
2025-06-28 03:05:23 - NodeExecutor - DEBUG - Added correlation_id f23d086f-aae9-4de8-9d1f-916862c870cb to payload
2025-06-28 03:05:23 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 03:05:23 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': '5ef6984f-9ad4-47b0-8352-feae2d45b56e', 'correlation_id': 'f23d086f-aae9-4de8-9d1f-916862c870cb', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 03:05:23 - NodeExecutor - DEBUG - Request 5ef6984f-9ad4-47b0-8352-feae2d45b56e sent successfully using provided producer.
2025-06-28 03:05:23 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 5ef6984f-9ad4-47b0-8352-feae2d45b56e...
2025-06-28 03:05:24 - NodeExecutor - DEBUG - Result consumer received message: Offset=849
2025-06-28 03:05:24 - NodeExecutor - DEBUG - Received valid result for request_id 5ef6984f-9ad4-47b0-8352-feae2d45b56e
2025-06-28 03:05:24 - NodeExecutor - INFO - Result received for request 5ef6984f-9ad4-47b0-8352-feae2d45b56e.
2025-06-28 03:05:24 - TransitionHandler - INFO - Execution result from Components executor: "1\ncoming"
2025-06-28 03:05:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '1\ncoming', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 03:05:24 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '1\ncoming'}, 'status': 'completed', 'timestamp': 1751060124.464832}}
2025-06-28 03:05:25 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 03:05:25 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 03:05:25 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 03:05:25 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 03:05:25 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 03:05:25 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 03:05:25 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 03:05:25 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 03:05:25 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 03:05:25 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 03:05:25 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 03:05:25 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 03:05:25 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 03:05:25 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 03:05:25 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 3.17 seconds
2025-06-28 03:05:25 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id f23d086f-aae9-4de8-9d1f-916862c870cb):
2025-06-28 03:05:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'result': 'Completed transition in 3.17 seconds', 'message': 'Transition completed in 3.17 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-28 03:05:41 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 03:05:41 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 03:05:42 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 03:05:42 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 03:06:12 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 03:06:13 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 03:06:13 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 03:06:13 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 03:06:13 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 03:06:13 - EnhancedWorkflowEngine - WARNING - Workflow f23d086f-aae9-4de8-9d1f-916862c870cb execution was cancelled!
2025-06-28 03:06:13 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 03:06:13 - KafkaWorkflowConsumer - WARNING - Workflow execution for 'feda07bf-a91e-4004-80cb-72416cdb5a43' was cancelled
2025-06-28 03:06:13 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: cancelled, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled
2025-06-28 03:06:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: f23d086f-aae9-4de8-9d1f-916862c870cb, response: {'status': 'Workflow Cancelled', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-28 03:06:13 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: f23d086f-aae9-4de8-9d1f-916862c870cb 
2025-06-28 03:06:13 - Main - ERROR - Shutting down due to keyboard interrupt...
